<?php

use App\Http\Controllers\Admin\ApiSettingsController;
use App\Http\Controllers\Admin\ApiTokenController;
use Illuminate\Support\Facades\Route;

// These routes are included in the admin middleware group in web.php
// API Settings
Route::get('settings/api', [ApiSettingsController::class, 'index'])->name('settings.api');
Route::put('settings/api', [ApiSettingsController::class, 'update'])->name('settings.api.update');
Route::get('settings/api/documentation', [ApiSettingsController::class, 'documentation'])->name('settings.api.documentation');
Route::get('settings/api/documentation/download', [ApiSettingsController::class, 'downloadDocumentation'])->name('settings.api.documentation.download');

// API Token Management
Route::get('settings/api-tokens', [ApiTokenController::class, 'index'])->name('settings.api-tokens');
Route::post('settings/api-tokens', [ApiTokenController::class, 'store'])->name('settings.api-tokens.store');
Route::delete('settings/api-tokens/{token}', [ApiTokenController::class, 'destroy'])->name('settings.api-tokens.destroy');

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class QuizQuestion extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'quiz_id',
        'category_id',
        'question',
        'type',
        'options',
        'points',
        'sort_order',
        'is_required',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'options' => 'array',
        'points' => 'integer',
        'sort_order' => 'integer',
        'is_required' => 'boolean',
    ];

    /**
     * Get the quiz that owns this question.
     */
    public function quiz(): BelongsTo
    {
        return $this->belongsTo(Quiz::class);
    }

    /**
     * Get the category that owns this question.
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(QuizCategory::class, 'category_id');
    }

    /**
     * Get the question types available.
     */
    public static function getTypes(): array
    {
        return [
            'multiple_choice' => 'Multiple Choice',
            'single_choice' => 'Single Choice',
            'text' => 'Text Input',
            'scale' => 'Scale (1-10)',
        ];
    }

    /**
     * Check if this question has options.
     */
    public function hasOptions(): bool
    {
        return in_array($this->type, ['multiple_choice', 'single_choice']);
    }

    /**
     * Get formatted options for display.
     */
    public function getFormattedOptions(): array
    {
        if (!$this->hasOptions() || !$this->options) {
            return [];
        }

        $formatted = [];
        foreach ($this->options as $key => $option) {
            if (is_array($option)) {
                $formatted[$key] = $option;
            } else {
                // Legacy format - just text
                $formatted[$key] = [
                    'text' => $option,
                    'points' => 1
                ];
            }
        }

        return $formatted;
    }
}

<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Quiz;
use App\Models\QuizQuestion;
use App\Models\QuizCategory;
use App\Models\LandingPage;
use Illuminate\Http\Request;

class QuizAdminController extends Controller
{
    /**
     * Display the quiz for a landing page.
     */
    public function show(LandingPage $landingPage)
    {
        $quiz = $landingPage->quiz()->with(['questions.category', 'categories'])->first();

        if (!$quiz) {
            // Create a default quiz if none exists
            $quiz = Quiz::create([
                'landing_page_id' => $landingPage->id,
                'title' => 'Assessment Quiz',
                'description' => 'Please answer the following questions to help us understand your needs.',
                'is_active' => true,
            ]);
        }

        $categories = $quiz->categories()->ordered()->get();

        return view('admin.quizzes.show', compact('landingPage', 'quiz', 'categories'));
    }

    /**
     * Update the quiz.
     */
    public function update(Request $request, LandingPage $landingPage)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'is_active' => 'boolean',
        ]);

        $quiz = $landingPage->quiz()->firstOrFail();
        $quiz->update($validated);

        return redirect()->route('admin.quizzes.show', $landingPage)
            ->with('success', 'Quiz updated successfully.');
    }

    /**
     * Store a new question.
     */
    public function storeQuestion(Request $request, LandingPage $landingPage)
    {
        $validated = $request->validate([
            'question' => 'required|string|max:500',
            'type' => 'required|in:multiple_choice,single_choice,text,scale',
            'category_id' => 'nullable|exists:quiz_categories,id',
            'options' => 'nullable|array',
            'options.*.text' => 'required_if:type,multiple_choice,single_choice|string|max:255',
            'options.*.points' => 'required_if:type,multiple_choice,single_choice|integer|min:0',
            'points' => 'required|integer|min:1',
            'is_required' => 'boolean',
        ]);

        $quiz = $landingPage->quiz()->firstOrFail();

        // Get the next sort order
        $maxSortOrder = $quiz->questions()->max('sort_order') ?? 0;

        $question = QuizQuestion::create([
            'quiz_id' => $quiz->id,
            'category_id' => $validated['category_id'] ?? null,
            'question' => $validated['question'],
            'type' => $validated['type'],
            'options' => $validated['options'] ?? null,
            'points' => $validated['points'],
            'sort_order' => $maxSortOrder + 1,
            'is_required' => $validated['is_required'] ?? true,
        ]);

        return redirect()->route('admin.quizzes.show', $landingPage)
            ->with('success', 'Question added successfully.');
    }

    /**
     * Update a question.
     */
    public function updateQuestion(Request $request, LandingPage $landingPage, QuizQuestion $question)
    {
        $validated = $request->validate([
            'question' => 'required|string|max:500',
            'type' => 'required|in:multiple_choice,single_choice,text,scale',
            'category_id' => 'nullable|exists:quiz_categories,id',
            'options' => 'nullable|array',
            'options.*.text' => 'required_if:type,multiple_choice,single_choice|string|max:255',
            'options.*.points' => 'required_if:type,multiple_choice,single_choice|integer|min:0',
            'points' => 'required|integer|min:1',
            'is_required' => 'boolean',
        ]);

        $question->update([
            'question' => $validated['question'],
            'type' => $validated['type'],
            'category_id' => $validated['category_id'] ?? null,
            'options' => $validated['options'] ?? null,
            'points' => $validated['points'],
            'is_required' => $validated['is_required'] ?? true,
        ]);

        return redirect()->route('admin.quizzes.show', $landingPage)
            ->with('success', 'Question updated successfully.');
    }

    /**
     * Delete a question.
     */
    public function destroyQuestion(LandingPage $landingPage, QuizQuestion $question)
    {
        $question->delete();

        return redirect()->route('admin.quizzes.show', $landingPage)
            ->with('success', 'Question deleted successfully.');
    }

    /**
     * Reorder questions.
     */
    public function reorderQuestions(Request $request, LandingPage $landingPage)
    {
        $validated = $request->validate([
            'questions' => 'required|array',
            'questions.*' => 'required|integer|exists:quiz_questions,id',
        ]);

        $quiz = $landingPage->quiz()->firstOrFail();

        foreach ($validated['questions'] as $index => $questionId) {
            QuizQuestion::where('id', $questionId)
                ->where('quiz_id', $quiz->id)
                ->update(['sort_order' => $index + 1]);
        }

        return response()->json(['success' => true]);
    }
}

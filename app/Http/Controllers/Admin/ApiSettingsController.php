<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Setting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class ApiSettingsController extends Controller
{
    /**
     * Display the API settings page.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        $apiSettings = Setting::where('group', 'api')->get();
        return view('admin.settings.api', compact('apiSettings'));
    }

    /**
     * Display the API documentation page.
     *
     * @return \Illuminate\View\View
     */
    public function documentation()
    {
        return view('admin.settings.api-documentation');
    }

    /**
     * Download the API documentation as a Markdown file.
     *
     * @return \Illuminate\Http\Response
     */
    public function downloadDocumentation()
    {
        return response()->json([
            'message' => 'Documentation has been removed.',
            'status' => 'info'
        ]);
    }

    /**
     * Update API settings.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'allowed_api_ips' => 'required|string',
            'api_token_expiration' => 'required|integer|min:0',
            'api_rate_limit' => 'required|integer|min:1',
        ]);

        if ($validator->fails()) {
            return redirect()->route('admin.settings.api')
                ->withErrors($validator)
                ->withInput();
        }

        $validated = $validator->validated();

        // Update the environment variable for immediate effect
        $this->updateEnvVariable('ALLOWED_API_IPS', $validated['allowed_api_ips']);

        // Update settings in the database
        foreach ($validated as $key => $value) {
            Setting::set($key, $value);
        }

        return redirect()->route('admin.settings.api')
            ->with('success', 'API settings updated successfully.');
    }

    /**
     * Update an environment variable in the .env file.
     *
     * @param  string  $key
     * @param  string  $value
     * @return bool
     */
    private function updateEnvVariable($key, $value)
    {
        $path = base_path('.env');

        if (file_exists($path)) {
            $content = file_get_contents($path);

            // If the key exists, replace its value
            if (preg_match("/^{$key}=/m", $content)) {
                $content = preg_replace("/^{$key}=.*/m", "{$key}={$value}", $content);
            } else {
                // If the key doesn't exist, add it
                $content .= "\n{$key}={$value}\n";
            }

            file_put_contents($path, $content);
            return true;
        }

        return false;
    }
}

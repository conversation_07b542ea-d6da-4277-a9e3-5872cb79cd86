<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Log;

class SitemapController extends Controller
{
    /**
     * Serve the sitemap.xml file
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        // Try multiple possible locations for the sitemap file
        $possiblePaths = [
            public_path('sitemap.xml'),
            base_path('public/sitemap.xml'),
            base_path('sitemap.xml'),
            // Add more potential paths if needed
        ];

        foreach ($possiblePaths as $path) {
            if (file_exists($path)) {
                Log::info('Sitemap found at: ' . $path);

                // Set cache control headers to prevent caching issues
                $headers = [
                    'Content-Type' => 'application/xml',
                    'Cache-Control' => 'no-store, no-cache, must-revalidate, max-age=0',
                    'Pragma' => 'no-cache',
                    'Expires' => '0',
                ];

                return Response::make(
                    file_get_contents($path),
                    200,
                    $headers
                );
            }
        }

        // If we get here, no sitemap file was found
        Log::warning('Sitemap file not found in any of the expected locations');
        abort(404, 'Sitemap not found');
    }
}

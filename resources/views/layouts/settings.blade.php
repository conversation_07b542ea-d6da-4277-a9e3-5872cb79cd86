<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>{{ $title ?? config('app.name') }} - Settings</title>

    <!-- Favicon -->
    <link rel="icon" href="{{ asset('favicon.ico') }}" sizes="any">
    <link rel="icon" href="{{ asset('favicon.svg') }}" type="image/svg+xml">
    <link rel="apple-touch-icon" href="{{ asset('apple-touch-icon.png') }}">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Main Site CSS -->
    <link rel="stylesheet" href="{{ asset('css/styles.css') }}">

    <!-- Admin CSS -->
    <link rel="stylesheet" href="{{ asset('css/admin.css') }}">

    <!-- Custom Settings CSS -->
    <style>
        /* Override some admin styles for settings pages */
        .settings-container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .settings-sidebar {
            background-color: var(--admin-white);
            border-radius: 0.5rem;
            box-shadow: var(--admin-box-shadow);
            padding: 1.5rem;
            margin-bottom: 1.5rem;
        }

        .settings-content {
            background-color: var(--admin-white);
            border-radius: 0.5rem;
            box-shadow: var(--admin-box-shadow);
            padding: 1.5rem;
        }

        .settings-heading {
            margin-bottom: 1.5rem;
        }

        .settings-heading h1 {
            font-weight: 600;
            color: var(--admin-gray-800);
            margin-bottom: 0.5rem;
        }

        .settings-heading p {
            color: var(--admin-gray-600);
        }

        .nav-link {
            color: var(--admin-gray-700);
            padding: 0.75rem 1rem;
            border-radius: 0.375rem;
            margin-bottom: 0.5rem;
            transition: all 0.3s ease;
        }

        .nav-link:hover {
            background-color: var(--admin-primary-soft);
            color: var(--admin-primary);
        }

        .nav-link.active {
            background-color: var(--admin-primary-soft);
            color: var(--admin-primary);
            font-weight: 600;
        }

        .form-control:focus {
            border-color: var(--admin-primary);
            box-shadow: 0 0 0 0.25rem rgba(59, 130, 246, 0.25);
        }

        .btn-primary {
            background-color: var(--admin-primary);
            border-color: var(--admin-primary);
        }

        .btn-primary:hover {
            background-color: var(--admin-primary-dark);
            border-color: var(--admin-primary-dark);
            box-shadow: 0 4px 10px rgba(37, 99, 235, 0.3);
        }

        /* Card styling */
        .card-header.bg-primary {
            background-color: var(--admin-primary) !important;
            border-color: var(--admin-primary);
        }
    </style>
</head>
<body class="admin-body">
    <div class="container-fluid">
        <div class="row g-0">
            <!-- Sidebar -->
            <nav id="sidebar" class="col-md-3 col-lg-2 d-md-block bg-white shadow-sm sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="d-flex align-items-center justify-content-center mb-4 px-3">
                        <a href="{{ route('dashboard') }}" class="text-decoration-none">
                            <h5 class="mb-0 fw-bold">
                                <span class="logo-endpoint">Endpoint</span><span class="logo-sync">Sync</span>
                                <span class="badge bg-highlight ms-2">Settings</span>
                            </h5>
                        </a>
                    </div>
                    <ul class="nav flex-column px-3">
                        <li class="nav-item mb-2">
                            <a href="{{ route('dashboard') }}" class="nav-link admin-nav-link rounded {{ request()->routeIs('dashboard') ? 'active' : '' }}">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                Dashboard
                            </a>
                        </li>
                        <li class="nav-item mb-2">
                            <a href="{{ route('settings.profile') }}" class="nav-link admin-nav-link rounded {{ request()->routeIs('settings.profile') ? 'active' : '' }}">
                                <i class="fas fa-user me-2"></i>
                                Profile
                            </a>
                        </li>
                        <li class="nav-item mb-2">
                            <a href="{{ route('settings.password') }}" class="nav-link admin-nav-link rounded {{ request()->routeIs('settings.password') ? 'active' : '' }}">
                                <i class="fas fa-key me-2"></i>
                                Password
                            </a>
                        </li>
                        <li class="nav-item mb-2">
                            <a href="{{ route('settings.appearance') }}" class="nav-link admin-nav-link rounded {{ request()->routeIs('settings.appearance') ? 'active' : '' }}">
                                <i class="fas fa-palette me-2"></i>
                                Appearance
                            </a>
                        </li>
                        @if(auth()->user()->is_admin)
                        <li class="nav-item mb-2">
                            <a href="{{ route('admin.dashboard') }}" class="nav-link admin-nav-link rounded">
                                <i class="fas fa-shield-alt me-2"></i>
                                Admin Dashboard
                            </a>
                        </li>
                        @endif
                    </ul>

                    <div class="dropdown px-3 mt-4">
                        <a href="#" class="d-flex align-items-center text-decoration-none dropdown-toggle" id="dropdownUser" data-bs-toggle="dropdown" aria-expanded="false">
                            <div class="rounded-circle bg-blue-soft d-flex align-items-center justify-content-center me-2" style="width: 38px; height: 38px;">
                                <span class="text-primary fw-bold">{{ auth()->user()->initials() }}</span>
                            </div>
                            <div>
                                <strong>{{ auth()->user()->name }}</strong>
                                <div class="small text-muted">User</div>
                            </div>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end shadow-sm border-0" aria-labelledby="dropdownUser">
                            <li><a class="dropdown-item" href="{{ route('dashboard') }}"><i class="fas fa-tachometer-alt me-2"></i> Dashboard</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <form method="POST" action="{{ route('logout') }}">
                                    @csrf
                                    <button type="submit" class="dropdown-item">
                                        <i class="fas fa-sign-out-alt me-2"></i> Sign out
                                    </button>
                                </form>
                            </li>
                        </ul>
                    </div>
                </div>
            </nav>

            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 py-4 bg-admin-gray-100 min-vh-100">
                <!-- Mobile header -->
                <div class="d-md-none border-bottom pb-3 mb-4">
                    <div class="d-flex justify-content-between align-items-center">
                        <button class="btn btn-outline-secondary border-0" type="button" data-bs-toggle="collapse" data-bs-target="#sidebar">
                            <i class="fas fa-bars"></i>
                        </button>
                        <a href="{{ route('dashboard') }}" class="text-decoration-none">
                            <h5 class="mb-0 fw-bold">
                                <span class="logo-endpoint">Endpoint</span><span class="logo-sync">Sync</span>
                                <span class="badge bg-highlight ms-2">Settings</span>
                            </h5>
                        </a>
                        <div class="dropdown">
                            <a href="#" class="d-flex align-items-center text-decoration-none" id="dropdownUserMobile" data-bs-toggle="dropdown" aria-expanded="false">
                                <div class="rounded-circle bg-blue-soft d-flex align-items-center justify-content-center" style="width: 38px; height: 38px;">
                                    <span class="text-primary fw-bold">{{ auth()->user()->initials() }}</span>
                                </div>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end shadow-sm border-0" aria-labelledby="dropdownUserMobile">
                                <li><a class="dropdown-item" href="{{ route('dashboard') }}"><i class="fas fa-tachometer-alt me-2"></i> Dashboard</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <form method="POST" action="{{ route('logout') }}">
                                        @csrf
                                        <button type="submit" class="dropdown-item">
                                            <i class="fas fa-sign-out-alt me-2"></i> Sign out
                                        </button>
                                    </form>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Alert messages -->
                @if (session('success'))
                    <div class="alert alert-success d-flex align-items-center mb-4 shadow-sm border-0" role="alert">
                        <i class="fas fa-check-circle me-2"></i>
                        <div>
                            {{ session('success') }}
                        </div>
                    </div>
                @endif

                @if (session('error'))
                    <div class="alert alert-danger d-flex align-items-center mb-4 shadow-sm border-0" role="alert">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        <div>
                            {{ session('error') }}
                        </div>
                    </div>
                @endif

                <!-- Page content -->
                <div class="content-wrapper bg-white p-4 rounded shadow-sm mb-4">
                    {{ $slot }}
                </div>
            </main>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

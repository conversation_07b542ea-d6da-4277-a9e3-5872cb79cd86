@extends('components.layouts.admin')

@section('title', 'Landing Pages')

@section('content')
    <!-- Content Header (Page header) -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">{{ __('Landing Pages') }}</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item active">Landing Pages</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <!-- Small boxes (Stat box) -->
            <div class="row">
                <div class="col-lg-3 col-6">
                    <div class="small-box bg-info">
                        <div class="inner">
                            <h3>{{ $landingPages->total() }}</h3>
                            <p>{{ __('Total Pages') }}</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-file-alt"></i>
                        </div>
                        <a href="{{ route('admin.landing-pages.index') }}" class="small-box-footer">
                            {{ __('View All') }} <i class="fas fa-arrow-circle-right"></i>
                        </a>
                    </div>
                </div>
                <div class="col-lg-3 col-6">
                    <div class="small-box bg-success">
                        <div class="inner">
                            <h3>{{ $landingPages->where('is_active', true)->count() }}</h3>
                            <p>{{ __('Active Pages') }}</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <a href="{{ route('admin.landing-pages.index') }}" class="small-box-footer">
                            {{ __('View Active') }} <i class="fas fa-arrow-circle-right"></i>
                        </a>
                    </div>
                </div>
                <div class="col-lg-3 col-6">
                    <div class="small-box bg-warning">
                        <div class="inner">
                            <h3>{{ $landingPages->where('has_quiz', true)->count() }}</h3>
                            <p>{{ __('With Quiz') }}</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-question-circle"></i>
                        </div>
                        <a href="{{ route('admin.landing-pages.index') }}" class="small-box-footer">
                            {{ __('View Quiz Pages') }} <i class="fas fa-arrow-circle-right"></i>
                        </a>
                    </div>
                </div>
                <div class="col-lg-3 col-6">
                    <div class="small-box bg-danger">
                        <div class="inner">
                            <h3>{{ $landingPages->sum('leads_count') }}</h3>
                            <p>{{ __('Total Leads') }}</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <a href="{{ route('admin.leads.index') }}" class="small-box-footer">
                            {{ __('View Leads') }} <i class="fas fa-arrow-circle-right"></i>
                        </a>
                    </div>
                </div>
            </div>
            <!-- /.row -->

            <!-- Landing Pages Table -->
            @if($landingPages->count() > 0)
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">{{ __('All Landing Pages') }}</h3>
                        <div class="card-tools">
                            <span class="badge badge-primary mr-2">{{ $landingPages->total() }} {{ __('Total') }}</span>
                            <a href="{{ route('admin.landing-pages.create') }}" class="btn btn-primary btn-sm">
                                <i class="fas fa-plus mr-2"></i>Create Landing Page
                            </a>
                        </div>
                    </div>
                    <!-- /.card-header -->
                    <div class="card-body table-responsive p-0">
                        <table class="table table-hover text-nowrap">
                            <thead>
                                <tr>
                                    <th>Landing Page</th>
                                    <th>Status</th>
                                    <th>Leads</th>
                                    <th>Quiz</th>
                                    <th>Created</th>
                                    <th>URL</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($landingPages as $landingPage)
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center mr-3" style="width: 40px; height: 40px;">
                                                    <i class="fas fa-file-alt text-white"></i>
                                                </div>
                                                <div>
                                                    <strong>{{ $landingPage->title }}</strong>
                                                    @if($landingPage->meta_description)
                                                        <br><small class="text-muted">{{ Str::limit($landingPage->meta_description, 60) }}</small>
                                                    @endif
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge {{ $landingPage->is_active ? 'badge-success' : 'badge-secondary' }}">
                                                {{ $landingPage->is_active ? 'Active' : 'Inactive' }}
                                            </span>
                                        </td>
                                        <td>
                                            <span class="font-weight-bold text-primary">{{ $landingPage->leads_count }}</span>
                                            <small class="text-muted"> leads</small>
                                        </td>
                                        <td>
                                            @if($landingPage->has_quiz)
                                                <span class="badge badge-info mr-2">Quiz</span>
                                                <small class="text-muted">{{ $landingPage->quiz ? $landingPage->quiz->questions->count() : 0 }} questions</small>
                                            @else
                                                <span class="text-muted">No quiz</span>
                                            @endif
                                        </td>
                                        <td>
                                            <div>{{ $landingPage->created_at->format('M j, Y') }}</div>
                                            <small class="text-muted">{{ $landingPage->created_at->diffForHumans() }}</small>
                                        </td>
                                        <td>
                                            <code class="small">/landing/{{ Str::limit($landingPage->slug, 15) }}</code>
                                        </td>
                                        <td>
                                            <div class="btn-group">
                                                <a href="{{ route('admin.landing-pages.show', $landingPage) }}" class="btn btn-sm btn-info" title="View Details">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="{{ route('admin.landing-pages.edit', $landingPage) }}" class="btn btn-sm btn-warning" title="Edit">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                @if($landingPage->has_quiz)
                                                    <a href="{{ route('admin.quizzes.show', $landingPage) }}" class="btn btn-sm btn-success" title="Manage Quiz">
                                                        <i class="fas fa-question-circle"></i>
                                                    </a>
                                                @endif
                                                <a href="{{ route('landing-pages.show', $landingPage) }}" target="_blank" class="btn btn-sm btn-secondary" title="Preview">
                                                    <i class="fas fa-external-link-alt"></i>
                                                </a>
                                                <form method="POST" action="{{ route('admin.landing-pages.destroy', $landingPage) }}" class="d-inline" onsubmit="return confirm('Are you sure you want to delete this landing page?')">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="btn btn-sm btn-danger" title="Delete">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                    <!-- /.card-body -->
                    @if($landingPages->hasPages())
                        <div class="card-footer clearfix">
                            {{ $landingPages->links() }}
                        </div>
                    @endif
                </div>
                <!-- /.card -->
            @else
                <div class="card">
                    <div class="card-body">
                        <div class="text-center py-5">
                            <i class="fas fa-file-alt fa-4x text-muted mb-3"></i>
                            <h4 class="text-muted mb-3">No Landing Pages Yet</h4>
                            <p class="text-muted mb-4">Create your first landing page to start generating leads and building your conversion funnel.</p>
                            <a href="{{ route('admin.landing-pages.create') }}" class="btn btn-primary">
                                <i class="fas fa-plus mr-2"></i>Create Your First Landing Page
                            </a>
                        </div>
                    </div>
                </div>
            @endif


        </div>
        <!-- /.container-fluid -->
    </section>
    <!-- /.content -->
@endsection

@extends('components.layouts.admin')

@section('title', 'Edit Landing Page')

@section('content')
    <!-- Content Header (Page header) -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">{{ __('Edit Landing Page') }}</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('admin.landing-pages.index') }}">Landing Pages</a></li>
                        <li class="breadcrumb-item active">Edit</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <!-- <PERSON> Header Card -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Edit Landing Page: {{ $landingPage->title }}</h3>
                    <div class="card-tools">
                        <a href="{{ route('admin.landing-pages.show', $landingPage) }}" class="btn btn-info btn-sm">
                            <i class="fas fa-eye mr-2"></i>{{ __('View') }}
                        </a>
                        <a href="{{ route('admin.landing-pages.index') }}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left mr-2"></i>{{ __('Back') }}
                        </a>
                    </div>
                </div>
            </div>

            @if(session('success'))
                <div class="alert alert-success alert-dismissible">
                    <button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>
                    <i class="icon fas fa-check"></i> {{ session('success') }}
                </div>
            @endif

<form method="POST" action="{{ route('admin.landing-pages.update', $landingPage) }}">
    @csrf
    @method('PUT')

            <div class="row">
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Page Content</h3>
                        </div>
                        <div class="card-body">
                            <div class="form-group">
                                <label for="title">Title *</label>
                                <input type="text"
                                       class="form-control @error('title') is-invalid @enderror"
                                       id="title"
                                       name="title"
                                       value="{{ old('title', $landingPage->title) }}"
                                       required>
                                @error('title')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="form-group">
                                <label for="slug">URL Slug</label>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">{{ url('/landing/') }}/</span>
                                    </div>
                                    <input type="text"
                                           class="form-control @error('slug') is-invalid @enderror"
                                           id="slug"
                                           name="slug"
                                           value="{{ old('slug', $landingPage->slug) }}">
                                    @error('slug')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                <small class="form-text text-muted">
                                    Current URL: <a href="{{ route('landing-pages.show', $landingPage) }}" target="_blank">{{ route('landing-pages.show', $landingPage) }}</a>
                                </small>
                            </div>

                            <div class="form-group">
                                <label for="content">Content *</label>
                                <textarea class="form-control @error('content') is-invalid @enderror"
                                          id="content"
                                          name="content"
                                          rows="20"
                                          required>{{ old('content', $landingPage->content) }}</textarea>
                                @error('content')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <small class="form-text text-muted">Use HTML to format your content. This will be displayed on the landing page.</small>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Page Settings</h3>
                        </div>
                        <div class="card-body">
                            <div class="form-group">
                                <div class="custom-control custom-switch">
                                    <input type="checkbox"
                                           class="custom-control-input"
                                           id="is_active"
                                           name="is_active"
                                           value="1"
                                           {{ old('is_active', $landingPage->is_active) ? 'checked' : '' }}>
                                    <label class="custom-control-label" for="is_active">Active</label>
                                </div>
                                <small class="form-text text-muted">Only active pages are visible to visitors</small>
                            </div>

                            <div class="form-group">
                                <div class="custom-control custom-switch">
                                    <input type="checkbox"
                                           class="custom-control-input"
                                           id="has_quiz"
                                           name="has_quiz"
                                           value="1"
                                           {{ old('has_quiz', $landingPage->has_quiz) ? 'checked' : '' }}>
                                    <label class="custom-control-label" for="has_quiz">Enable Quiz</label>
                                </div>
                                <small class="form-text text-muted">Show quiz button and enable assessment flow</small>
                            </div>
                        </div>
                    </div>

                    <div class="card mt-3">
                        <div class="card-header">
                            <h3 class="card-title">SEO Settings</h3>
                        </div>
                        <div class="card-body">
                            <div class="form-group">
                                <label for="meta_title">Meta Title</label>
                                <input type="text"
                                       class="form-control @error('meta_title') is-invalid @enderror"
                                       id="meta_title"
                                       name="meta_title"
                                       value="{{ old('meta_title', $landingPage->meta_title) }}"
                                       maxlength="60">
                                @error('meta_title')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <small class="form-text text-muted">Leave blank to use page title</small>
                            </div>

                            <div class="form-group">
                                <label for="meta_description">Meta Description</label>
                                <textarea class="form-control @error('meta_description') is-invalid @enderror"
                                          id="meta_description"
                                          name="meta_description"
                                          rows="3"
                                          maxlength="160">{{ old('meta_description', $landingPage->meta_description) }}</textarea>
                                @error('meta_description')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <small class="form-text text-muted">Recommended: 150-160 characters</small>
                            </div>
                        </div>
                    </div>

                    <div class="card mt-3">
                        <div class="card-body">
                            <button type="submit" class="btn btn-primary btn-block">
                                <i class="fas fa-save mr-2"></i>Update Landing Page
                            </button>
                        </div>
                    </div>

                    @if($landingPage->has_quiz)
                        <div class="card mt-3">
                            <div class="card-header">
                                <h3 class="card-title">Quiz Management</h3>
                            </div>
                            <div class="card-body">
                                <a href="{{ route('admin.quizzes.show', $landingPage) }}" class="btn btn-info btn-block">
                                    <i class="fas fa-question-circle mr-2"></i>Manage Quiz
                                </a>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </form>

<!-- Include TinyMCE for rich text editing -->
<script src="https://cdn.tiny.cloud/1/laqmad4amsu52pi0fiou1nb91hscak79b3jsijl5340v0a8s/tinymce/6/tinymce.min.js" referrerpolicy="origin"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize TinyMCE
    tinymce.init({
        selector: '#content',
        height: 500,
        menubar: false,
        plugins: [
            'advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'preview',
            'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen',
            'insertdatetime', 'media', 'table', 'help', 'wordcount'
        ],
        toolbar: 'undo redo | blocks | ' +
            'bold italic backcolor | alignleft aligncenter ' +
            'alignright alignjustify | bullist numlist outdent indent | ' +
            'removeformat | help',
        content_style: 'body { font-family: -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; font-size: 14px; }'
    });
});
</script>
        </div>
        <!-- /.container-fluid -->
    </section>
    <!-- /.content -->
@endsection

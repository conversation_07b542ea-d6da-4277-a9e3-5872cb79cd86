@extends('components.layouts.admin')

@section('title', __('View Consultation'))

@section('content')
    <!-- Content Header (Page header) -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">{{ __('View Consultation') }}</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('admin.consultations.index') }}">Consultations</a></li>
                        <li class="breadcrumb-item active">View</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">{{ __('Consultation Details') }}</h3>
                    <div class="card-tools">
                        <a href="{{ route('admin.consultations.edit', $consultation) }}" class="btn btn-warning btn-sm">
                            <i class="fas fa-edit mr-2"></i>{{ __('Edit') }}
                        </a>
                        <a href="{{ route('admin.consultations.index') }}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left mr-2"></i>{{ __('Back') }}
                        </a>
                    </div>
                </div>

                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h5>{{ __('Contact Information') }}</h5>
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>{{ __('Name') }}:</strong></td>
                                    <td>{{ $consultation->name }}</td>
                                </tr>
                                <tr>
                                    <td><strong>{{ __('Email') }}:</strong></td>
                                    <td>{{ $consultation->email }}</td>
                                </tr>
                                <tr>
                                    <td><strong>{{ __('Website URL') }}:</strong></td>
                                    <td>
                                        @if ($consultation->website_url)
                                            <a href="{{ $consultation->website_url }}" target="_blank">{{ $consultation->website_url }}</a>
                                        @else
                                            <span class="text-muted">{{ __('Not provided') }}</span>
                                        @endif
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h5>{{ __('Status Information') }}</h5>
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>{{ __('Status') }}:</strong></td>
                                    <td>
                                        @if ($consultation->status)
                                            <span class="badge mr-2" style="background-color: {{ $consultation->status->color }};">
                                                {{ $consultation->status->name }}
                                            </span>
                                        @else
                                            <span class="badge badge-secondary mr-2">{{ __('No Status') }}</span>
                                        @endif

                                        <div class="btn-group">
                                            <button type="button" class="btn btn-sm btn-outline-secondary dropdown-toggle" data-toggle="dropdown">
                                                {{ __('Change Status') }}
                                            </button>
                                            <div class="dropdown-menu">
                                                @foreach($statuses as $status)
                                                    @if(!$consultation->status || $consultation->status->id != $status->id)
                                                        <form action="{{ route('admin.consultations.update-status', $consultation) }}" method="POST" class="d-inline">
                                                            @csrf
                                                            @method('PATCH')
                                                            <input type="hidden" name="status_id" value="{{ $status->id }}">
                                                            <button type="submit" class="dropdown-item">
                                                                <span class="badge mr-2" style="background-color: {{ $status->color }};">
                                                                    &nbsp;
                                                                </span>
                                                                {{ $status->name }}
                                                            </button>
                                                        </form>
                                                    @endif
                                                @endforeach
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>{{ __('Submitted On') }}:</strong></td>
                                    <td>{{ $consultation->created_at->format('F j, Y \a\t g:i A') }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <hr>

                    <h5>{{ __('Business Information') }}</h5>
                    <table class="table table-borderless">
                        <tr>
                            <td><strong>{{ __('Business Description') }}:</strong></td>
                            <td>{{ $consultation->business_description }}</td>
                        </tr>
                        <tr>
                            <td><strong>{{ __('Pain Points') }}:</strong></td>
                            <td>{{ $consultation->pain_point }}</td>
                        </tr>
                        <tr>
                            <td><strong>{{ __('Current Automation') }}:</strong></td>
                            <td>{{ $consultation->current_automation }}</td>
                        </tr>
                        @if ($consultation->additional_notes)
                            <tr>
                                <td><strong>{{ __('Additional Notes') }}:</strong></td>
                                <td>{{ $consultation->additional_notes }}</td>
                            </tr>
                        @endif
                    </table>
                </div>

                <div class="card-footer">
                    <div class="row">
                        <div class="col-md-6">
                            <form action="{{ route('admin.consultations.destroy', $consultation) }}" method="POST" onsubmit="return confirm('{{ __('Are you sure you want to delete this consultation?') }}')">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="btn btn-danger">
                                    <i class="fas fa-trash mr-2"></i>{{ __('Delete Consultation') }}
                                </button>
                            </form>
                        </div>
                        <div class="col-md-6 text-right">
                            <a href="{{ route('admin.consultations.index') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left mr-2"></i>{{ __('Back to List') }}
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- /.container-fluid -->
    </section>
    <!-- /.content -->
@endsection

@extends('components.layouts.admin')

@section('title', __('Site Settings'))

@section('content')
    <!-- Content Header (Page header) -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">{{ __('Site Settings') }}</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item active">Settings</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <!-- Small boxes (Stat box) -->
            <div class="row">
                <div class="col-lg-3 col-6">
                    <!-- small box -->
                    <div class="small-box bg-info">
                        <div class="inner">
                            <h3>{{ App\Models\Setting::where('key', 'LIKE', 'social_%')->whereNotNull('value')->where('value', '!=', '')->count() }}</h3>
                            <p>{{ __('Social Links') }}</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-share-alt"></i>
                        </div>
                        <a href="{{ route('admin.settings.social-media') }}" class="small-box-footer">
                            {{ __('Manage Social Media') }} <i class="fas fa-arrow-circle-right"></i>
                        </a>
                    </div>
                </div>
                <!-- ./col -->
                <div class="col-lg-3 col-6">
                    <!-- small box -->
                    <div class="small-box bg-success">
                        <div class="inner">
                            <h3>{{ App\Models\ConsultationStatus::count() }}</h3>
                            <p>{{ __('Status Types') }}</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-tags"></i>
                        </div>
                        <a href="{{ route('admin.consultation-statuses.index') }}" class="small-box-footer">
                            {{ __('Manage Statuses') }} <i class="fas fa-arrow-circle-right"></i>
                        </a>
                    </div>
                </div>
                <!-- ./col -->
                <div class="col-lg-3 col-6">
                    <!-- small box -->
                    <div class="small-box bg-warning">
                        <div class="inner">
                            <h3>{{ App\Models\Setting::get('allowed_api_ips') ? count(explode(',', App\Models\Setting::get('allowed_api_ips'))) : 0 }}</h3>
                            <p>{{ __('Allowed IPs') }}</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <a href="{{ route('admin.settings.api') }}" class="small-box-footer">
                            {{ __('API Settings') }} <i class="fas fa-arrow-circle-right"></i>
                        </a>
                    </div>
                </div>
                <!-- ./col -->
                <div class="col-lg-3 col-6">
                    <!-- small box -->
                    <div class="small-box bg-danger">
                        <div class="inner">
                            <h3>{{ \Laravel\Sanctum\PersonalAccessToken::count() }}</h3>
                            <p>{{ __('API Tokens') }}</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-key"></i>
                        </div>
                        <a href="{{ route('admin.settings.api-tokens') }}" class="small-box-footer">
                            {{ __('Manage Tokens') }} <i class="fas fa-arrow-circle-right"></i>
                        </a>
                    </div>
                </div>
            </div>
            <!-- Settings Management Section -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-cogs mr-2"></i>{{ __('Settings Management') }}
                            </h3>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <!-- Social Media Settings -->
                                <div class="col-lg-6 col-md-12">
                                    <div class="info-box">
                                        <span class="info-box-icon bg-info"><i class="fas fa-share-alt"></i></span>
                                        <div class="info-box-content">
                                            <span class="info-box-text">{{ __('Social Media Settings') }}</span>
                                            <span class="info-box-number">
                                                {{ App\Models\Setting::where('key', 'LIKE', 'social_%')->whereNotNull('value')->where('value', '!=', '')->count() }} {{ __('configured') }}
                                            </span>
                                            <div class="progress">
                                                <div class="progress-bar bg-info" style="width: {{ (App\Models\Setting::where('key', 'LIKE', 'social_%')->whereNotNull('value')->where('value', '!=', '')->count() / 4) * 100 }}%"></div>
                                            </div>
                                            <span class="progress-description">
                                                {{ __('Configure social media links for website footer') }}
                                            </span>
                                            <div class="mt-2">
                                                <a href="{{ route('admin.settings.social-media') }}" class="btn btn-info btn-sm">
                                                    <i class="fas fa-cog mr-1"></i>{{ __('Configure') }}
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Consultation Status Settings -->
                                <div class="col-lg-6 col-md-12">
                                    <div class="info-box">
                                        <span class="info-box-icon bg-success"><i class="fas fa-tags"></i></span>
                                        <div class="info-box-content">
                                            <span class="info-box-text">{{ __('Consultation Status Types') }}</span>
                                            <span class="info-box-number">{{ App\Models\ConsultationStatus::count() }} {{ __('types') }}</span>
                                            <div class="progress">
                                                <div class="progress-bar bg-success" style="width: {{ min(App\Models\ConsultationStatus::count() * 25, 100) }}%"></div>
                                            </div>
                                            <span class="progress-description">
                                                {{ __('Manage consultation workflow statuses') }}
                                            </span>
                                            <div class="mt-2">
                                                <a href="{{ route('admin.consultation-statuses.index') }}" class="btn btn-success btn-sm">
                                                    <i class="fas fa-cog mr-1"></i>{{ __('Manage') }}
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- API Settings -->
                                <div class="col-lg-6 col-md-12">
                                    <div class="info-box">
                                        <span class="info-box-icon bg-warning"><i class="fas fa-shield-alt"></i></span>
                                        <div class="info-box-content">
                                            <span class="info-box-text">{{ __('API Security Settings') }}</span>
                                            <span class="info-box-number">
                                                {{ App\Models\Setting::get('allowed_api_ips') ? count(explode(',', App\Models\Setting::get('allowed_api_ips'))) : 0 }} {{ __('IPs allowed') }}
                                            </span>
                                            <div class="progress">
                                                <div class="progress-bar bg-warning" style="width: {{ App\Models\Setting::get('allowed_api_ips') ? 100 : 20 }}%"></div>
                                            </div>
                                            <span class="progress-description">
                                                {{ __('Configure API access and security settings') }}
                                            </span>
                                            <div class="mt-2">
                                                <a href="{{ route('admin.settings.api') }}" class="btn btn-warning btn-sm">
                                                    <i class="fas fa-cog mr-1"></i>{{ __('Configure') }}
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- API Tokens -->
                                <div class="col-lg-6 col-md-12">
                                    <div class="info-box">
                                        <span class="info-box-icon bg-danger"><i class="fas fa-key"></i></span>
                                        <div class="info-box-content">
                                            <span class="info-box-text">{{ __('API Access Tokens') }}</span>
                                            <span class="info-box-number">{{ \Laravel\Sanctum\PersonalAccessToken::count() }} {{ __('active') }}</span>
                                            <div class="progress">
                                                <div class="progress-bar bg-danger" style="width: {{ min(\Laravel\Sanctum\PersonalAccessToken::count() * 20, 100) }}%"></div>
                                            </div>
                                            <span class="progress-description">
                                                {{ __('Create and manage API access tokens') }}
                                            </span>
                                            <div class="mt-2">
                                                <a href="{{ route('admin.settings.api-tokens') }}" class="btn btn-danger btn-sm">
                                                    <i class="fas fa-cog mr-1"></i>{{ __('Manage') }}
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions Section -->
            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-bolt mr-2"></i>{{ __('Quick Actions') }}
                            </h3>
                        </div>
                        <div class="card-body">
                            <div class="btn-group-vertical btn-block">
                                <a href="{{ route('admin.settings.social-media') }}" class="btn btn-outline-primary mb-2">
                                    <i class="fas fa-share-alt mr-2"></i>{{ __('Update Social Media Links') }}
                                </a>
                                <a href="{{ route('admin.consultation-statuses.create') }}" class="btn btn-outline-success mb-2">
                                    <i class="fas fa-plus mr-2"></i>{{ __('Add New Status Type') }}
                                </a>
                                <a href="{{ route('admin.settings.api-tokens') }}" class="btn btn-outline-danger">
                                    <i class="fas fa-key mr-2"></i>{{ __('Create API Token') }}
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-info-circle mr-2"></i>{{ __('System Information') }}
                            </h3>
                        </div>
                        <div class="card-body">
                            <dl class="row">
                                <dt class="col-sm-6">{{ __('Total Settings:') }}</dt>
                                <dd class="col-sm-6">{{ App\Models\Setting::count() }}</dd>

                                <dt class="col-sm-6">{{ __('Social Links:') }}</dt>
                                <dd class="col-sm-6">
                                    <span class="badge badge-info">
                                        {{ App\Models\Setting::where('key', 'LIKE', 'social_%')->whereNotNull('value')->where('value', '!=', '')->count() }}/4
                                    </span>
                                </dd>

                                <dt class="col-sm-6">{{ __('Status Types:') }}</dt>
                                <dd class="col-sm-6">
                                    <span class="badge badge-success">{{ App\Models\ConsultationStatus::count() }}</span>
                                </dd>

                                <dt class="col-sm-6">{{ __('API Tokens:') }}</dt>
                                <dd class="col-sm-6">
                                    <span class="badge badge-danger">{{ \Laravel\Sanctum\PersonalAccessToken::count() }}</span>
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- /.container-fluid -->
    </section>
    <!-- /.content -->
@endsection

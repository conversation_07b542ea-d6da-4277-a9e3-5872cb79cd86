@extends('layouts.app')

@section('title', 'Thank You - ' . $landingPage->title)

@section('content')
<div class="thank-you-container">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="thank-you-content text-center">
                    <div class="success-animation mb-4">
                        <div class="checkmark-circle">
                            <div class="checkmark"></div>
                        </div>
                    </div>
                    
                    <h1>Thank You, {{ $lead->name }}!</h1>
                    <p class="lead mb-4">Your consultation request has been successfully submitted.</p>
                    
                    <div class="confirmation-details">
                        <div class="card">
                            <div class="card-body">
                                <h3 class="card-title">
                                    <i class="fas fa-calendar-check me-2"></i>
                                    What Happens Next?
                                </h3>
                                
                                <div class="timeline">
                                    <div class="timeline-item">
                                        <div class="timeline-icon">
                                            <i class="fas fa-clock"></i>
                                        </div>
                                        <div class="timeline-content">
                                            <h5>Within 24 Hours</h5>
                                            <p>Our team will review your assessment and availability, then contact you to schedule your consultation.</p>
                                        </div>
                                    </div>
                                    
                                    <div class="timeline-item">
                                        <div class="timeline-icon">
                                            <i class="fas fa-file-pdf"></i>
                                        </div>
                                        <div class="timeline-content">
                                            <h5>Personalized Report</h5>
                                            <p>You'll receive a detailed PDF report with your assessment results and initial recommendations.</p>
                                        </div>
                                    </div>
                                    
                                    <div class="timeline-item">
                                        <div class="timeline-icon">
                                            <i class="fas fa-handshake"></i>
                                        </div>
                                        <div class="timeline-content">
                                            <h5>Free Consultation</h5>
                                            <p>We'll discuss your specific needs and how we can help optimize your business processes.</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="contact-details mt-4">
                        <div class="card">
                            <div class="card-body">
                                <h4>Your Submission Details</h4>
                                <div class="details-grid">
                                    <div class="detail-item">
                                        <strong>Name:</strong> {{ $lead->name }}
                                    </div>
                                    <div class="detail-item">
                                        <strong>Email:</strong> {{ $lead->email }}
                                    </div>
                                    <div class="detail-item">
                                        <strong>Company:</strong> {{ $lead->company_name }}
                                    </div>
                                    @if($lead->phone_number)
                                        <div class="detail-item">
                                            <strong>Phone:</strong> {{ $lead->phone_number }}
                                        </div>
                                    @endif
                                    @if($lead->quiz_score !== null)
                                        <div class="detail-item">
                                            <strong>Assessment Score:</strong> {{ $lead->quiz_score }}
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="additional-resources mt-4">
                        <h4>While You Wait</h4>
                        <p class="text-muted mb-3">Explore more resources to learn about business automation:</p>
                        <div class="resource-links">
                            <a href="{{ route('home') }}" class="btn btn-outline-primary me-3">
                                <i class="fas fa-home me-2"></i>Visit Our Homepage
                            </a>
                            <a href="{{ route('home') }}#about" class="btn btn-outline-secondary">
                                <i class="fas fa-info-circle me-2"></i>Learn More About Us
                            </a>
                        </div>
                    </div>
                    
                    <div class="contact-support mt-5">
                        <p class="text-muted">
                            <small>
                                <i class="fas fa-question-circle me-1"></i>
                                Have questions? Contact us at 
                                <a href="mailto:<EMAIL>"><EMAIL></a>
                            </small>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.thank-you-container {
    padding: 3rem 0;
    min-height: 80vh;
}

.success-animation {
    margin: 2rem 0;
}

.checkmark-circle {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background: var(--success);
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: scaleIn 0.5s ease-out;
}

.checkmark {
    width: 40px;
    height: 20px;
    border: 4px solid white;
    border-top: none;
    border-right: none;
    transform: rotate(-45deg);
    animation: checkmarkDraw 0.3s ease-out 0.2s both;
}

@keyframes scaleIn {
    0% {
        transform: scale(0);
    }
    100% {
        transform: scale(1);
    }
}

@keyframes checkmarkDraw {
    0% {
        width: 0;
        height: 0;
    }
    100% {
        width: 40px;
        height: 20px;
    }
}

.thank-you-content h1 {
    color: var(--dark);
    font-weight: 700;
    margin-bottom: 1rem;
    font-size: 2.5rem;
}

.card {
    border: none;
    border-radius: 15px;
    box-shadow: var(--box-shadow);
    margin-bottom: 2rem;
}

.card-title {
    color: var(--dark);
    font-weight: 600;
    margin-bottom: 2rem;
}

.timeline {
    text-align: left;
}

.timeline-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 2rem;
    position: relative;
}

.timeline-item:not(:last-child)::after {
    content: '';
    position: absolute;
    left: 24px;
    top: 48px;
    width: 2px;
    height: 40px;
    background: var(--gray-light);
}

.timeline-icon {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: var(--gradient-primary);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    flex-shrink: 0;
}

.timeline-content h5 {
    color: var(--dark);
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.timeline-content p {
    color: var(--gray);
    margin: 0;
    line-height: 1.6;
}

.details-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    text-align: left;
}

.detail-item {
    padding: 0.75rem;
    background: var(--secondary);
    border-radius: 8px;
    color: var(--gray);
}

.detail-item strong {
    color: var(--dark);
}

.resource-links {
    margin-top: 1rem;
}

.btn-outline-primary {
    border: 2px solid var(--primary);
    color: var(--primary);
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    border-radius: 50px;
    transition: all 0.3s ease;
}

.btn-outline-primary:hover {
    background: var(--primary);
    color: white;
    transform: translateY(-2px);
}

.btn-outline-secondary {
    border: 2px solid var(--gray-light);
    color: var(--gray);
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    border-radius: 50px;
    transition: all 0.3s ease;
}

.btn-outline-secondary:hover {
    background: var(--gray);
    border-color: var(--gray);
    color: white;
    transform: translateY(-2px);
}

.contact-support a {
    color: var(--primary);
    text-decoration: none;
}

.contact-support a:hover {
    text-decoration: underline;
}

@media (max-width: 768px) {
    .thank-you-container {
        padding: 2rem 0;
    }
    
    .thank-you-content h1 {
        font-size: 2rem;
    }
    
    .checkmark-circle {
        width: 100px;
        height: 100px;
    }
    
    .checkmark {
        width: 30px;
        height: 15px;
    }
    
    .details-grid {
        grid-template-columns: 1fr;
    }
    
    .resource-links {
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }
    
    .timeline-item {
        flex-direction: column;
        text-align: center;
    }
    
    .timeline-item::after {
        display: none;
    }
    
    .timeline-icon {
        margin: 0 auto 1rem auto;
    }
}
</style>
@endsection

@extends('layouts.app')

@section('title', $quiz->title . ' - ' . $landingPage->title)

@section('content')
<div class="quiz-container">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="quiz-header text-center mb-5">
                    <h1>{{ $quiz->title }}</h1>
                    @if($quiz->description)
                        <p class="lead">{{ $quiz->description }}</p>
                    @endif
                    <div class="progress-container">
                        <div class="progress">
                            <div class="progress-bar" role="progressbar" style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
                        </div>
                        <small class="text-muted">Question <span id="current-question">1</span> of {{ $quiz->questions->count() }}</small>
                    </div>
                </div>

                <form id="quiz-form" method="POST" action="{{ route('landing-pages.quiz.submit', $landingPage) }}">
                    @csrf

                    <!-- Lead Information Section -->
                    <div class="quiz-section" id="lead-info-section">
                        <div class="card">
                            <div class="card-body">
                                <h3 class="card-title">Your Information</h3>
                                <p class="text-muted mb-4">Please provide your contact information to receive your personalized report.</p>

                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="name" class="form-label">Full Name *</label>
                                        <input type="text" class="form-control" id="name" name="name" required value="{{ old('name') }}">
                                        @error('name')
                                            <div class="text-danger small">{{ $message }}</div>
                                        @enderror
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="email" class="form-label">Email Address *</label>
                                        <input type="email" class="form-control" id="email" name="email" required value="{{ old('email') }}">
                                        @error('email')
                                            <div class="text-danger small">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="company_name" class="form-label">Company Name *</label>
                                        <input type="text" class="form-control" id="company_name" name="company_name" required value="{{ old('company_name') }}">
                                        @error('company_name')
                                            <div class="text-danger small">{{ $message }}</div>
                                        @enderror
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="phone_number" class="form-label">Phone Number</label>
                                        <input type="tel" class="form-control" id="phone_number" name="phone_number" value="{{ old('phone_number') }}">
                                        @error('phone_number')
                                            <div class="text-danger small">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quiz Questions -->
                    @foreach($quiz->questions as $index => $question)
                        <div class="quiz-section question-section" id="question-{{ $index + 1 }}" style="display: none;">
                            <div class="card">
                                <div class="card-body">
                                    @if($question->category)
                                        <div class="question-category mb-3">
                                            <span class="badge category-badge" style="background-color: {{ $question->category->color }}; color: white;">
                                                <i class="fas fa-folder me-1"></i>{{ $question->category->name }}
                                            </span>
                                            @if($question->category->description)
                                                <small class="text-muted d-block mt-1">{{ $question->category->description }}</small>
                                            @endif
                                        </div>
                                    @endif
                                    <h3 class="card-title">{{ $question->question }}</h3>

                                    @if($question->type === 'multiple_choice')
                                        @foreach($question->getFormattedOptions() as $key => $option)
                                            <div class="form-check mb-3">
                                                <input class="form-check-input" type="checkbox"
                                                       name="quiz_responses[{{ $question->id }}][]"
                                                       value="{{ $key }}"
                                                       id="q{{ $question->id }}_{{ $key }}">
                                                <label class="form-check-label" for="q{{ $question->id }}_{{ $key }}">
                                                    {{ $option['text'] }}
                                                </label>
                                            </div>
                                        @endforeach

                                    @elseif($question->type === 'single_choice')
                                        @foreach($question->getFormattedOptions() as $key => $option)
                                            <div class="form-check mb-3">
                                                <input class="form-check-input" type="radio"
                                                       name="quiz_responses[{{ $question->id }}]"
                                                       value="{{ $key }}"
                                                       id="q{{ $question->id }}_{{ $key }}">
                                                <label class="form-check-label" for="q{{ $question->id }}_{{ $key }}">
                                                    {{ $option['text'] }}
                                                </label>
                                            </div>
                                        @endforeach

                                    @elseif($question->type === 'scale')
                                        <div class="scale-container">
                                            <div class="scale-labels d-flex justify-content-between mb-2">
                                                <small class="text-muted">1 - Poor</small>
                                                <small class="text-muted">10 - Excellent</small>
                                            </div>
                                            <div class="scale-options d-flex justify-content-between">
                                                @for($i = 1; $i <= 10; $i++)
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="radio"
                                                               name="quiz_responses[{{ $question->id }}]"
                                                               value="{{ $i }}"
                                                               id="q{{ $question->id }}_{{ $i }}">
                                                        <label class="form-check-label" for="q{{ $question->id }}_{{ $i }}">
                                                            {{ $i }}
                                                        </label>
                                                    </div>
                                                @endfor
                                            </div>
                                        </div>

                                    @elseif($question->type === 'text')
                                        <textarea class="form-control"
                                                  name="quiz_responses[{{ $question->id }}]"
                                                  rows="4"
                                                  placeholder="Please provide your answer..."></textarea>
                                    @endif
                                </div>
                            </div>
                        </div>
                    @endforeach

                    <!-- Navigation Buttons -->
                    <div class="quiz-navigation d-flex justify-content-between mt-4">
                        <button type="button" class="btn btn-outline-secondary" id="prev-btn" style="display: none;">
                            <i class="fas fa-arrow-left me-2"></i>Previous
                        </button>
                        <button type="button" class="btn btn-primary" id="next-btn">
                            Next<i class="fas fa-arrow-right ms-2"></i>
                        </button>
                        <button type="submit" class="btn btn-success" id="submit-btn" style="display: none;">
                            <i class="fas fa-check me-2"></i>Complete Assessment
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<style>
.quiz-container {
    padding: 2rem 0;
    min-height: 80vh;
}

.quiz-header h1 {
    color: var(--dark);
    font-weight: 700;
    margin-bottom: 1rem;
}

.progress-container {
    max-width: 400px;
    margin: 2rem auto;
}

.progress {
    height: 8px;
    border-radius: 10px;
    background-color: var(--gray-light);
}

.progress-bar {
    background: var(--gradient-primary);
    border-radius: 10px;
    transition: width 0.3s ease;
}

.card {
    border: none;
    border-radius: 15px;
    box-shadow: var(--box-shadow);
    margin-bottom: 2rem;
}

.card-title {
    color: var(--dark);
    font-weight: 600;
    margin-bottom: 1.5rem;
}

.form-check {
    padding: 1rem;
    border-radius: 10px;
    transition: background-color 0.2s ease;
}

.form-check:hover {
    background-color: var(--primary-soft);
}

.form-check-input:checked + .form-check-label {
    color: var(--primary);
    font-weight: 600;
}

.question-category {
    margin-bottom: 1rem;
}

.category-badge {
    font-size: 0.875rem;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 600;
}

.scale-container {
    padding: 1rem;
}

.scale-options .form-check {
    flex-direction: column;
    align-items: center;
    padding: 0.5rem;
    margin: 0;
}

.btn-primary, .btn-success {
    background: var(--gradient-primary);
    border: none;
    padding: 0.75rem 2rem;
    font-weight: 600;
    border-radius: 50px;
    transition: all 0.3s ease;
}

.btn-primary:hover, .btn-success:hover {
    transform: translateY(-2px);
    box-shadow: var(--box-shadow-hover);
}

.btn-outline-secondary {
    border: 2px solid var(--gray-light);
    color: var(--gray);
    padding: 0.75rem 2rem;
    font-weight: 600;
    border-radius: 50px;
    transition: all 0.3s ease;
}

.btn-outline-secondary:hover {
    background-color: var(--gray);
    border-color: var(--gray);
    color: white;
}

@media (max-width: 768px) {
    .quiz-container {
        padding: 1rem 0;
    }

    .card-body {
        padding: 1.5rem;
    }

    .scale-options {
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .scale-options .form-check {
        flex: 0 0 18%;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const sections = document.querySelectorAll('.quiz-section');
    const totalSections = sections.length;
    let currentSection = 0;

    const prevBtn = document.getElementById('prev-btn');
    const nextBtn = document.getElementById('next-btn');
    const submitBtn = document.getElementById('submit-btn');
    const progressBar = document.querySelector('.progress-bar');
    const currentQuestionSpan = document.getElementById('current-question');

    function showSection(index) {
        sections.forEach((section, i) => {
            section.style.display = i === index ? 'block' : 'none';
        });

        // Update progress
        const progress = ((index + 1) / totalSections) * 100;
        progressBar.style.width = progress + '%';
        currentQuestionSpan.textContent = index + 1;

        // Update buttons
        prevBtn.style.display = index > 0 ? 'inline-block' : 'none';
        nextBtn.style.display = index < totalSections - 1 ? 'inline-block' : 'none';
        submitBtn.style.display = index === totalSections - 1 ? 'inline-block' : 'none';
    }

    function validateCurrentSection() {
        const currentSectionEl = sections[currentSection];
        const requiredInputs = currentSectionEl.querySelectorAll('input[required], textarea[required]');

        for (let input of requiredInputs) {
            if (!input.value.trim()) {
                input.focus();
                return false;
            }
        }

        return true;
    }

    nextBtn.addEventListener('click', function() {
        if (validateCurrentSection() && currentSection < totalSections - 1) {
            currentSection++;
            showSection(currentSection);
        }
    });

    prevBtn.addEventListener('click', function() {
        if (currentSection > 0) {
            currentSection--;
            showSection(currentSection);
        }
    });

    // Initialize
    showSection(0);
});
</script>
@endsection

@section('additional_head')
<meta name="csrf-token" content="{{ csrf_token() }}">
@endsection

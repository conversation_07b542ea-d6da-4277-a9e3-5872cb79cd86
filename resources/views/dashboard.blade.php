<x-layouts.app :title="__('Dashboard')">
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="card border-0 shadow-sm">
                    <div class="card-body p-4">
                        <h1 class="h3 fw-bold mb-3">{{ __('Welcome to EndpointSync') }}</h1>
                        <p class="text-muted mb-4">{{ __('Your automation journey starts here.') }}</p>
                        
                        <div class="row g-4 mb-4">
                            <div class="col-md-6">
                                <div class="card h-100 border-0 shadow-sm">
                                    <div class="card-body p-4">
                                        <div class="d-flex align-items-center mb-3">
                                            <div class="me-3 d-flex align-items-center justify-content-center rounded-circle bg-primary-soft" style="width: 48px; height: 48px;">
                                                <i class="fas fa-user text-primary"></i>
                                            </div>
                                            <h5 class="card-title fw-bold mb-0">{{ __('Your Profile') }}</h5>
                                        </div>
                                        <p class="card-text">{{ __('Manage your account settings and preferences.') }}</p>
                                        <a href="{{ route('settings.profile') }}" class="btn btn-primary" wire:navigate>
                                            {{ __('Go to Profile') }}
                                            <i class="fas fa-arrow-right ms-1"></i>
                                        </a>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="card h-100 border-0 shadow-sm">
                                    <div class="card-body p-4">
                                        <div class="d-flex align-items-center mb-3">
                                            <div class="me-3 d-flex align-items-center justify-content-center rounded-circle bg-primary-soft" style="width: 48px; height: 48px;">
                                                <i class="fas fa-lock text-primary"></i>
                                            </div>
                                            <h5 class="card-title fw-bold mb-0">{{ __('Security') }}</h5>
                                        </div>
                                        <p class="card-text">{{ __('Update your password and security settings.') }}</p>
                                        <a href="{{ route('settings.password') }}" class="btn btn-primary" wire:navigate>
                                            {{ __('Manage Security') }}
                                            <i class="fas fa-arrow-right ms-1"></i>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="alert alert-info">
                            <div class="d-flex">
                                <div class="me-3">
                                    <i class="fas fa-info-circle fa-2x"></i>
                                </div>
                                <div>
                                    <h5 class="alert-heading fw-bold">{{ __('Need Help?') }}</h5>
                                    <p class="mb-0">{{ __('If you have any questions or need assistance, please contact our support team.') }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-layouts.app>

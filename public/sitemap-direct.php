<?php
// This file serves the sitemap.xml directly, bypassing <PERSON><PERSON> routing

// Path to the sitemap file
$sitemapPath = __DIR__ . '/sitemap.xml';

// Check if the file exists
if (!file_exists($sitemapPath)) {
    header('HTTP/1.0 404 Not Found');
    echo 'Sitemap not found';
    exit;
}

// Set appropriate headers
header('Content-Type: application/xml');
header('Cache-Control: no-store, no-cache, must-revalidate, max-age=0');
header('Pragma: no-cache');
header('Expires: 0');

// Output the sitemap content
echo file_get_contents($sitemapPath);

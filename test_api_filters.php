<?php
/**
 * Simple script to test the consultation API filtering functionality
 * 
 * Usage: php test_api_filters.php
 */

// Configuration
$baseUrl = 'http://localhost:8001/api/v1';
$apiToken = 'YOUR_API_TOKEN'; // Replace with a valid API token

// Test cases
$testCases = [
    [
        'name' => 'Get all consultations (no filters)',
        'endpoint' => '/consultations',
        'params' => [],
    ],
    [
        'name' => 'Filter by name',
        'endpoint' => '/consultations',
        'params' => ['name' => '<PERSON>'],
    ],
    [
        'name' => 'Filter by contacted status',
        'endpoint' => '/consultations',
        'params' => ['contacted' => 'false'],
    ],
    [
        'name' => 'Filter with pagination',
        'endpoint' => '/consultations',
        'params' => ['page' => '1', 'per_page' => '5'],
    ],
    [
        'name' => 'Filter with sorting',
        'endpoint' => '/consultations',
        'params' => ['sort_by' => 'name', 'sort_direction' => 'asc'],
    ],
    [
        'name' => 'Combined filters',
        'endpoint' => '/consultations',
        'params' => [
            'contacted' => 'false',
            'sort_by' => 'created_at',
            'sort_direction' => 'desc',
            'page' => '1',
            'per_page' => '10'
        ],
    ],
];

// Function to make API requests
function makeRequest($url, $token) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Accept: application/json',
        'Authorization: Bearer ' . $token,
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    return [
        'code' => $httpCode,
        'body' => $response,
    ];
}

// Run tests
echo "Testing Consultation API Filtering\n";
echo "=================================\n\n";

foreach ($testCases as $test) {
    echo "Test: {$test['name']}\n";
    
    // Build URL with query parameters
    $url = $baseUrl . $test['endpoint'];
    if (!empty($test['params'])) {
        $url .= '?' . http_build_query($test['params']);
    }
    
    echo "URL: $url\n";
    
    // Make request
    $result = makeRequest($url, $apiToken);
    
    // Display results
    echo "Status Code: {$result['code']}\n";
    
    if ($result['code'] == 200) {
        $data = json_decode($result['body'], true);
        
        if (json_last_error() === JSON_ERROR_NONE) {
            // Format and display response summary
            echo "Success: " . ($data['success'] ? 'true' : 'false') . "\n";
            
            if (isset($data['data'])) {
                echo "Results: " . count($data['data']) . " consultations\n";
                
                if (isset($data['meta'])) {
                    echo "Pagination: Page {$data['meta']['current_page']} of {$data['meta']['last_page']}, ";
                    echo "Total: {$data['meta']['total']} consultations\n";
                }
            }
        } else {
            echo "Error parsing JSON response\n";
        }
    } else {
        echo "Response: {$result['body']}\n";
    }
    
    echo "\n";
}

echo "Tests completed.\n";
